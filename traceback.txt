﻿I'm sorry, but an uncaught exception occurred.

While running game code:
  File "game/options.rpy", line 6, in script
    define config.name_base = "Tenderness-Beyond-the-Wall"
Exception: config.name_base is not a known configuration variable.

-- Full Traceback ------------------------------------------------------------

Traceback (most recent call last):
  File "game/options.rpy", line 6, in script
    define config.name_base = "Tenderness-Beyond-the-Wall"
  File "renpy/ast.py", line 2452, in execute
    self.set()
    ~~~~~~~~^^
  File "renpy/ast.py", line 2468, in set
    ns.set(self.varname, value)
    ~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "renpy/common/000namespaces.rpy", line 34, in set
    setattr(self.nso, name, value)
    ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "renpy/defaultstore.py", line 100, in __setattr__
    raise Exception("config.%s is not a known configuration variable." % (name))
Exception: config.name_base is not a known configuration variable.

Windows-11-10.0.22631-SP0 AMD64
Ren'Py 8.4.1.25072401
隔牆的溫柔 1.0
Sun Jul 27 18:28:16 2025
