2025-07-27 10:26:01 UTC
Windows-11-10.0.22631-SP0
Ren'Py 8.4.1.25072401

Early init took 46 ms
Loading error handling took 26 ms
Loading script took 628 ms
Loading save slot metadata took 2 ms
Loading persistent took 0 ms

File "game/options.rpy", line 61: expected '=' not found.
    define config.keymap['game_menu'].append('mouseup_3')
                                     ^

File "game/options.rpy", line 62: expected '=' not found.
    define config.keymap['hide_windows'].append('mouseup_2')
                                        ^
DPI scale factor: 1.000000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: Disabled thread optimizations.
Interface start took 389 ms

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: 'NVIDIA Corporation'
Renderer: 'NVIDIA GeForce RTX 3070 Laptop GPU/PCIe/SSE2'
Version: '4.6.0 NVIDIA 576.88'
Display Info: <Info({'bitsize': 32, 'bytesize': 4, 'masks': (16711680, 65280, 255, 0), 'shifts': (16, 8, 0, 0), 'losses': (0, 0, 0, 8), 'current_w': 1920, 'current_h': 1080, 'refresh_rate': 60, 'hw': False, 'wm': True, 'video_mem': 268435456, 'blit_hw': False, 'blit_hw_CC': False, 'blit_hw_A': False, 'blit_sw': False, 'blit_sw_CC': False, 'blit_sw_A': False})>
Screen sizes: virtual=(1280, 720) physical=(1280, 720) drawable=(1280, 720)
Maximum texture size: 4096x4096
Could not open 'cache/shaders.txt':
Resetting cache.
Early init took 4 ms
Loading error handling took 5 ms
Loading script took 90 ms
Loading save slot metadata took 2 ms
Loading persistent took 0 ms

File "game/options.rpy", line 61: expected '=' not found.
    define config.keymap['game_menu'].append('mouseup_3')
                                     ^

File "game/options.rpy", line 62: expected '=' not found.
    define config.keymap['hide_windows'].append('mouseup_2')
                                        ^
DPI scale factor: 1.000000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: Disabled thread optimizations.
Interface start took 267 ms

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: 'NVIDIA Corporation'
Renderer: 'NVIDIA GeForce RTX 3070 Laptop GPU/PCIe/SSE2'
Version: '4.6.0 NVIDIA 576.88'
Display Info: <Info({'bitsize': 32, 'bytesize': 4, 'masks': (16711680, 65280, 255, 0), 'shifts': (16, 8, 0, 0), 'losses': (0, 0, 0, 8), 'current_w': 1920, 'current_h': 1080, 'refresh_rate': 60, 'hw': False, 'wm': True, 'video_mem': 268435456, 'blit_hw': False, 'blit_hw_CC': False, 'blit_hw_A': False, 'blit_sw': False, 'blit_sw_CC': False, 'blit_sw_A': False})>
Screen sizes: virtual=(1280, 720) physical=(1280, 720) drawable=(1280, 720)
Maximum texture size: 4096x4096
Could not open 'cache/shaders.txt':
Resetting cache.
Early init took 4 ms
Loading error handling took 5 ms
Loading script took 89 ms
Loading save slot metadata took 2 ms
Loading persistent took 0 ms

File "game/options.rpy", line 61: expected '=' not found.
    define config.keymap['game_menu'].append('mouseup_3')
                                     ^

File "game/options.rpy", line 62: expected '=' not found.
    define config.keymap['hide_windows'].append('mouseup_2')
                                        ^
DPI scale factor: 1.000000
nvdrs: Loaded, about to disable thread optimizations.
nvdrs: Disabled thread optimizations.
Interface start took 255 ms

Initializing gl2 renderer:
primary display bounds: (0, 0, 1920, 1080)
swap interval: 1 frames
Windowed mode.
Vendor: 'NVIDIA Corporation'
Renderer: 'NVIDIA GeForce RTX 3070 Laptop GPU/PCIe/SSE2'
Version: '4.6.0 NVIDIA 576.88'
Display Info: <Info({'bitsize': 32, 'bytesize': 4, 'masks': (16711680, 65280, 255, 0), 'shifts': (16, 8, 0, 0), 'losses': (0, 0, 0, 8), 'current_w': 1920, 'current_h': 1080, 'refresh_rate': 60, 'hw': False, 'wm': True, 'video_mem': 268435456, 'blit_hw': False, 'blit_hw_CC': False, 'blit_hw_A': False, 'blit_sw': False, 'blit_sw_CC': False, 'blit_sw_A': False})>
Screen sizes: virtual=(1280, 720) physical=(1280, 720) drawable=(1280, 720)
Maximum texture size: 4096x4096
Could not open 'cache/shaders.txt':
